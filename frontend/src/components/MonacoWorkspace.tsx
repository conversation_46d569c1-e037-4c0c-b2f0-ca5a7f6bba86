import React, { useState, useEffect, useRef } from 'react';
import {
    <PERSON>,
    Typography,
    Alert,
    CircularProgress,
    Button,
    IconButton,
    List,
    ListItemButton,
    ListItemText,
    ListItemIcon,
    Collapse,
    Paper,
    Tooltip,
    Switch,
    FormControlLabel,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions
} from '@mui/material';
import {
    Save as SaveIcon,
    Undo as RevertIcon,
    Folder as FolderIcon,
    FolderOpen as FolderOpenIcon,
    InsertDriveFile as FileIcon,
    ExpandLess,
    ExpandMore,
    Refresh as RefreshIcon,
    OpenInNew as OpenInNewIcon,
    Stop as StopIcon,
    Visibility as VisibilityIcon,
    VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import Editor from '@monaco-editor/react';
import { ecsWorkspaceAPI, lifecycleAPI, configAPI } from '../utils/api';

interface MonacoWorkspaceProps {
    interviewUuid?: string;
    onWorkspaceReady?: (workspaceId: string) => void;
    onError?: (error: string) => void;
    height?: string | number;
    minHeight?: string | number;
}

interface FileNode {
    name: string;
    path: string;
    type: 'file' | 'folder';
    content?: string;
    language?: string;
    children?: FileNode[];
    isExpanded?: boolean;
    lastModified?: Date;
}

interface WorkspaceState {
    files: { [path: string]: string };
    savedFiles: { [path: string]: string };
    fileTree: FileNode[];
    selectedFile: string | null;
    hasUnsavedChanges: boolean;
}

interface WorkspaceUIState {
    expandedFolders: Set<string>;
    selectedFile: string | null;
    isPreviewOpen?: boolean;
    isExplorerOpen?: boolean;
}

const MonacoWorkspace: React.FC<MonacoWorkspaceProps> = ({
    interviewUuid,
    onWorkspaceReady, // Keep for compatibility but not used
    onError,
    height = '100%',
    minHeight = '500px'
}) => {
    console.log('🎯 MonacoWorkspace: *** COMPONENT MOUNTED *** - This should only happen after build is complete!');
    console.log('🎯 MonacoWorkspace: Interview UUID:', interviewUuid);
    const [workspace, setWorkspace] = useState<WorkspaceState>({
        files: {},
        savedFiles: {},
        fileTree: [],
        selectedFile: null,
        hasUnsavedChanges: false
    });

    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [isBuildComplete, setIsBuildComplete] = useState(false);
    const [previewStatus, setPreviewStatus] = useState<'idle' | 'starting' | 'running' | 'error'>('idle');
    const [previewError, setPreviewError] = useState<string | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [backendStatus, setBackendStatus] = useState<'idle' | 'starting' | 'running' | 'error'>('idle');
    const [backendUrl, setBackendUrl] = useState<string | null>(null);
    const [isAutoSaveEnabled, setIsAutoSaveEnabled] = useState(true); // Auto-save enabled by default
    const [isAutoSaving, setIsAutoSaving] = useState(false);
    const [currentWorkspaceId, setCurrentWorkspaceId] = useState<string | null>(null);
    const [lastExtendAt, setLastExtendAt] = useState<number>(0);
    const [lastExpiresAt, setLastExpiresAt] = useState<number | null>(null);
    const [minutesRemaining, setMinutesRemaining] = useState<number | null>(null);
    const [isExpired, setIsExpired] = useState<boolean>(false);
    const [extendModalOpen, setExtendModalOpen] = useState<boolean>(false);
    const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(true);
    const [isExplorerOpen, setIsExplorerOpen] = useState<boolean>(true);
    const [config, setConfig] = useState<{
      extendLifetimeHours: number;
      frontendLifetimeExtensionThresholdMs: number;
      frontendThrottleIntervalMs: number;
      frontendCheckCadenceMs: number;
      autoExtensionThresholdMinutes: number;
    } | null>(null);

    const editorRef = useRef<any>(null);
    const workspaceNameRef = useRef<string>(`workspace_${interviewUuid || Date.now()}`);
    const autoSaveTimeoutRef = useRef<number | null>(null);
    const fetchTimeoutRef = useRef<number | null>(null);
    const loadedFilesRef = useRef<Set<string>>(new Set());
    const latestWorkspaceRef = useRef<WorkspaceState>({
        files: {},
        savedFiles: {},
        fileTree: [],
        selectedFile: null,
        hasUnsavedChanges: false
    });

    // Helper: evaluate remaining time and extend if under threshold
    const maybeExtendLifetime = async (isInitialCheck = false) => {
        try {
            if (!currentWorkspaceId) return;
            
            const now = Date.now();
            
            // For initial check, skip throttling to show lifetime immediately
            if (!isInitialCheck) {
                // Throttle extend checks using configured interval
                if (now - lastExtendAt < (config?.frontendThrottleIntervalMs || 60000)) return;
            }

            // Get status to know expiresAt
            const status = await ecsWorkspaceAPI.getWorkspaceStatus(workspaceNameRef.current);
            const expiresAtIso = status?.data?.expiresAt;
            if (!expiresAtIso) return;
            const expiresAtMs = new Date(expiresAtIso).getTime();
            const msLeft = expiresAtMs - now;
            const extensionThresholdMs = config?.frontendLifetimeExtensionThresholdMs || 15 * 60 * 1000;
            
            // Check if backend has extended the workspace (expiresAt has increased)
            if (lastExpiresAt && expiresAtMs > lastExpiresAt) {
                console.log(`⏫ Backend auto-extended workspace lifetime (expiresAt: ${new Date(lastExpiresAt).toISOString()} -> ${new Date(expiresAtMs).toISOString()})`);
                console.log(`🔔 Setting modal to open for backend extension`);
                setExtendModalOpen(true);
            }
            
            // Always calculate and set minutes remaining, even if expired
            if (msLeft > 0) {
                setMinutesRemaining(Math.ceil(msLeft / 60000));
                setIsExpired(false);
            } else {
                // Workspace has expired
                setMinutesRemaining(0);
                setIsExpired(true);
            }
            
            // Update last expiresAt for next comparison
            setLastExpiresAt(expiresAtMs);
            
            // Only auto-extend if not initial check and under threshold
            if (!isInitialCheck && msLeft > 0 && msLeft <= extensionThresholdMs && config) {
                // Extend by configured hours
                const extendHours = config.extendLifetimeHours;
                await lifecycleAPI.extendWorkspaceLifetime(currentWorkspaceId, extendHours);
                setLastExtendAt(Date.now());
                console.log(`⏫ Frontend auto-extended workspace lifetime by ${extendHours}h`);
                console.log(`🔔 Setting modal to open for frontend extension`);
                setExtendModalOpen(true);
            }
        } catch (e) {
            console.warn('Failed to auto-extend lifetime (frontend):', (e as any)?.message || e);
        }
    };

    // Get workspace UI state storage key
    const getWorkspaceUIStateKey = (): string => {
        return `workspace_ui_state_${interviewUuid || 'default'}`;
    };

    // Load workspace UI state from localStorage
    const loadWorkspaceUIState = (): WorkspaceUIState => {
        try {
            const stored = localStorage.getItem(getWorkspaceUIStateKey());
            if (stored) {
                const parsed = JSON.parse(stored);
                return {
                    expandedFolders: new Set(parsed.expandedFolders || []),
                    selectedFile: parsed.selectedFile || null,
                    isPreviewOpen: typeof parsed.isPreviewOpen === 'boolean' ? parsed.isPreviewOpen : true,
                    isExplorerOpen: typeof parsed.isExplorerOpen === 'boolean' ? parsed.isExplorerOpen : true
                };
            }
        } catch (error) {
            console.warn('Failed to load workspace UI state:', error);
        }
        return {
            expandedFolders: new Set(),
            selectedFile: null,
            isPreviewOpen: true,
            isExplorerOpen: true
        };
    };

    // Save workspace UI state to localStorage
    const saveWorkspaceUIState = (uiState: WorkspaceUIState) => {
        try {
            const current = loadWorkspaceUIState();
            const toStore = {
                expandedFolders: Array.from(uiState.expandedFolders),
                selectedFile: uiState.selectedFile,
                isPreviewOpen: typeof uiState.isPreviewOpen === 'boolean' ? uiState.isPreviewOpen : (current.isPreviewOpen ?? true),
                isExplorerOpen: typeof uiState.isExplorerOpen === 'boolean' ? uiState.isExplorerOpen : (current.isExplorerOpen ?? true)
            };
            localStorage.setItem(getWorkspaceUIStateKey(), JSON.stringify(toStore));
        } catch (error) {
            console.warn('Failed to save workspace UI state:', error);
        }
    };

    // Clear workspace UI state from localStorage
    const clearWorkspaceUIState = () => {
        try {
            localStorage.removeItem(getWorkspaceUIStateKey());
        } catch (error) {
            console.warn('Failed to clear workspace UI state:', error);
        }
    };

    // Load configuration from backend
    useEffect(() => {
        const loadConfig = async () => {
            try {
                const configResponse = await configAPI.getPublicConfig();
                if (configResponse.success) {
                    setConfig(configResponse.data);
                }
            } catch (error) {
                            console.warn('Failed to load configuration, using defaults:', error);
            // Fallback to default values
            setConfig({
              extendLifetimeHours: 1,
              frontendLifetimeExtensionThresholdMs: 15 * 60 * 1000, // 15 minutes
              frontendThrottleIntervalMs: 60 * 1000, // 1 minute
              frontendCheckCadenceMs: 60 * 1000, // 1 minute
              autoExtensionThresholdMinutes: 15
            });
            }
        };
        loadConfig();
    }, []);

    // Initialize preview open state from persisted UI state
    useEffect(() => {
        const ui = loadWorkspaceUIState();
        setIsPreviewOpen(ui.isPreviewOpen ?? true);
        setIsExplorerOpen(ui.isExplorerOpen ?? true);
    }, []);

    // Debug modal state changes
    useEffect(() => {
        console.log(`🔔 Modal state changed: ${extendModalOpen}`);
    }, [extendModalOpen]);

    // Compute all ancestor folder paths for a given file path
    const getAncestorFolders = (filePath: string): string[] => {
        const parts = filePath.split('/');
        // remove the file name
        parts.pop();
        const ancestors: string[] = [];
        for (let i = 0; i < parts.length; i++) {
            const ancestor = parts.slice(0, i + 1).join('/');
            if (ancestor) ancestors.push(ancestor);
        }
        return ancestors;
    };

    // Ensure the expandedFolders set contains all ancestors of the selected file
    const withAncestorsExpanded = (expanded: Set<string>, selectedFile: string | null): Set<string> => {
        if (!selectedFile) return expanded;
        const updated = new Set(expanded);
        for (const folder of getAncestorFolders(selectedFile)) {
            updated.add(folder);
        }
        return updated;
    };

    // Apply UI state to file tree (set folder expansion states)
    const applyUIStateToFileTree = (nodes: FileNode[], expandedFolders: Set<string>): FileNode[] => {
        return nodes.map(node => {
            if (node.type === 'folder') {
                const updatedNode: FileNode = {
                    ...node,
                    isExpanded: expandedFolders.has(node.path) // Use persisted state or default to false
                };
                if (node.children) {
                    updatedNode.children = applyUIStateToFileTree(node.children, expandedFolders);
                }
                return updatedNode;
            }
            return node;
        });
    };

    // Initialize workspace when component mounts
    useEffect(() => {
        if (interviewUuid && !isInitialized) {
            console.log('🚀 MonacoWorkspace: Starting initialization for interview:', interviewUuid);
            initializeWorkspace();
        }
    }, [interviewUuid, isInitialized]);

    // Update latest workspace ref whenever workspace state changes
    useEffect(() => {
        latestWorkspaceRef.current = workspace;
    }, [workspace]);

    // Debug logging for state changes
    useEffect(() => {
        console.log('🔍 MonacoWorkspace: State update -', {
            isInitialized,
            isBuildComplete,
            hasFiles: Object.keys(workspace.files).length > 0,
            interviewUuid
        });
    }, [isInitialized, isBuildComplete, workspace.files, interviewUuid]);

    // Auto-load selected file content when selected file changes
    useEffect(() => {
        if (workspace.selectedFile && isInitialized) {
            const selectedFile = workspace.selectedFile;
            const currentContent = workspace.files[selectedFile];
            
            // If the selected file has no content or empty content, load it
            if (!currentContent || currentContent.trim() === '') {
                console.log('📂 Auto-loading content for selected file:', selectedFile);
                if (!loadedFilesRef.current.has(selectedFile)) {
                    loadedFilesRef.current.add(selectedFile);
                    loadFileContent(selectedFile);
                }
            }
        }
    }, [workspace.selectedFile, isInitialized]);

    // Cleanup timeouts on unmount
    useEffect(() => {
        return () => {
            if (autoSaveTimeoutRef.current) {
                clearTimeout(autoSaveTimeoutRef.current);
            }
            if (fetchTimeoutRef.current) {
                clearTimeout(fetchTimeoutRef.current);
            }
        };
    }, []);

    // Immediate lifetime calculation when workspace ID becomes available
    useEffect(() => {
        if (currentWorkspaceId) {
            console.log('⏰ Immediate lifetime calculation for workspace:', currentWorkspaceId);
            maybeExtendLifetime(true);
        }
    }, [currentWorkspaceId]);

    // Immediate lifetime calculation when config is loaded and workspace ID is available
    useEffect(() => {
        if (config && currentWorkspaceId) {
            console.log('⏰ Immediate lifetime calculation after config loaded for workspace:', currentWorkspaceId);
            maybeExtendLifetime(true);
        }
    }, [config, currentWorkspaceId]);

    // Periodic foreground heartbeat while component is active
    useEffect(() => {
        const id = window.setInterval(() => {
            maybeExtendLifetime(false);
        }, config?.frontendCheckCadenceMs || 60000); // Configurable check cadence
        return () => clearInterval(id);
    }, [currentWorkspaceId, lastExtendAt]);

    const initializeWorkspace = async () => {
        if (!interviewUuid) {
            setError('Interview UUID is required for workspace initialization');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            console.log('🚀 MonacoWorkspace: Initializing workspace for interview:', interviewUuid);

            // Ensure workspace name is correctly set from props
            workspaceNameRef.current = `workspace_${interviewUuid}`;

            // Always try to fetch from filesystem first, then fall back to empty workspace
            console.log('📡 MonacoWorkspace: Fetching workspace structure from backend filesystem');
            await fetchWorkspaceStructure();

            console.log('✅ MonacoWorkspace: Workspace structure fetched, marking as initialized');
            setIsInitialized(true);

            console.log('⏳ MonacoWorkspace: Initialized but waiting for build completion before calling onWorkspaceReady');
        } catch (error: any) {
            console.error('❌ MonacoWorkspace: Error initializing workspace:', error);
            setError(error.message || 'Failed to initialize workspace');
            onError?.(error.message || 'Failed to initialize workspace');
            console.log('⚠️ MonacoWorkspace: Error during initialization, will wait for build completion or timeout');
        } finally {
            setIsLoading(false);
        }
    };

    const fetchWorkspaceStructure = async (retryCount = 0, onComplete?: () => void) => {
        const maxRetries = 3;
        const retryDelay = 2000; // 2 seconds between retries
        
        // Clear any existing fetch timeout
        if (fetchTimeoutRef.current) {
            clearTimeout(fetchTimeoutRef.current);
            fetchTimeoutRef.current = null;
        }
        
        try {
            console.log(`📡 Fetching ECS workspace structure for: ${workspaceNameRef.current} (attempt ${retryCount + 1}/${maxRetries + 1})`);

            // Try to get ECS workspace structure
            const data = await ecsWorkspaceAPI.getStructure(workspaceNameRef.current);

            // Capture workspaceId for lifecycle API usage
            const wsId = data?.data?.workspaceInfo?.workspaceId || data?.workspaceInfo?.workspaceId;
            if (wsId) {
                setCurrentWorkspaceId(wsId);
                // Get initial lifetime status immediately
                maybeExtendLifetime(true);
            }

            if (data.success && data.data.structure) {
                const files = flattenStructureToFiles(data.data.structure);
                const fileTree = createFileTreeFromStructure(data.data.structure);
                const fileCount = Object.keys(files).length;

                console.log(`📁 Loaded ${fileCount} files from ECS workspace:`, Object.keys(files));
                console.log(`📁 File details:`, Object.keys(files).map(path => ({ path, size: files[path]?.length || 0 })));

                // Only retry if we have no files and haven't exceeded max retries
                if (fileCount === 0 && retryCount < maxRetries) {
                    console.log(`⏳ No files yet (attempt ${retryCount + 1}/${maxRetries + 1}), retrying in ${retryDelay}ms...`);
                    fetchTimeoutRef.current = window.setTimeout(() => {
                        fetchWorkspaceStructure(retryCount + 1, onComplete);
                    }, retryDelay);
                    return;
                }

                // Load and apply persisted UI state, auto-expand ancestors for selected file
                const uiState = loadWorkspaceUIState();
                const selectedFile = uiState.selectedFile && files[uiState.selectedFile] !== undefined ? uiState.selectedFile : Object.keys(files)[0] || null;
                const expandedWithAncestors = withAncestorsExpanded(new Set(uiState.expandedFolders), selectedFile);
                const updatedFileTree = applyUIStateToFileTree(fileTree, expandedWithAncestors);

                setWorkspace(prev => {
                    // Preserve any unsaved local edits
                    const unsavedPaths = Object.keys(prev.files).filter(p => (prev.files[p] ?? '') !== (prev.savedFiles[p] ?? ''));
                    const mergedFiles: { [path: string]: string } = { ...files };
                    
                    // Don't overwrite existing non-empty content with empty strings from structure
                    for (const p of unsavedPaths) {
                        if (prev.files[p] && prev.files[p].trim() !== '') {
                            mergedFiles[p] = prev.files[p];
                        }
                    }

                    // Compute new savedFiles without clobbering unsaved paths
                    const newSavedFiles: { [path: string]: string } = { ...prev.savedFiles };
                    for (const p of Object.keys(files)) {
                        if (!unsavedPaths.includes(p)) {
                            newSavedFiles[p] = files[p] as any;
                        }
                    }

                    const prevPaths = Object.keys(prev.files);
                    const nextPaths = Object.keys(mergedFiles);
                    const samePathSet = prevPaths.length === nextPaths.length && prevPaths.every(p => nextPaths.includes(p));
                    const sameContents = samePathSet && prevPaths.every(p => (prev.files[p] ?? '') === (mergedFiles[p] ?? ''));
                    if (sameContents) {
                        return prev; // No changes; avoid triggering editor refresh
                    }

                    return {
                        ...prev,
                        files: mergedFiles,
                        savedFiles: newSavedFiles,
                        fileTree: updatedFileTree,
                        selectedFile,
                        hasUnsavedChanges: unsavedPaths.length > 0 || Object.keys(mergedFiles).some(p => (mergedFiles[p] ?? '') !== (newSavedFiles[p] ?? ''))
                    };
                });

                // Persist updated expanded folders including ancestors
                saveWorkspaceUIState({
                    expandedFolders: expandedWithAncestors,
                    selectedFile
                });

                // Auto-load content for the selected file if it's not already loaded and has content
                if (selectedFile && !loadedFilesRef.current.has(selectedFile) && files[selectedFile] && files[selectedFile].trim() !== '') {
                    console.log('📂 Auto-loading content for restored selected file:', selectedFile);
                    loadedFilesRef.current.add(selectedFile);
                    loadFileContent(selectedFile);
                } else if (selectedFile && (!files[selectedFile] || files[selectedFile].trim() === '')) {
                    // If selected file exists but has no content, try to load it from the server
                    console.log('📂 Selected file has no content, loading from server:', selectedFile);
                    loadedFilesRef.current.add(selectedFile);
                    loadFileContent(selectedFile);
                }

                if (onComplete) {
                    console.log('✅ MonacoWorkspace: Workspace structure loaded, calling completion callback');
                    onComplete();
                }
            } else {
                console.log('📁 No structure found in ECS workspace, checking if workspace is still being created');

                if (retryCount < 3) {
                    console.log(`⏳ Retrying workspace structure fetch (attempt ${retryCount + 1}/3)`);
                    fetchTimeoutRef.current = window.setTimeout(() => fetchWorkspaceStructure(retryCount + 1, onComplete), 2000);
                    return;
                }

                setWorkspace(prev => ({
                    ...prev,
                    fileTree: [],
                    files: {},
                    savedFiles: {}
                }));

                if (onComplete) {
                    console.log('⚠️ MonacoWorkspace: Empty workspace after retries, calling completion callback');
                    onComplete();
                }
            }
        } catch (error: any) {
            console.error('❌ Error fetching ECS workspace structure:', error);

            if (retryCount < 3) {
                console.log(`⏳ Retrying workspace structure fetch after error (attempt ${retryCount + 1}/3)`);
                fetchTimeoutRef.current = window.setTimeout(() => fetchWorkspaceStructure(retryCount + 1, onComplete), 2000);
                return;
            }

            console.log('⚠️ Initializing empty workspace after retries failed');
            setWorkspace(prev => ({
                ...prev,
                fileTree: [],
                files: {},
                savedFiles: {}
            }));

            if (onComplete) {
                console.log('⚠️ MonacoWorkspace: Error after retries, calling completion callback');
                onComplete();
            }
        }
    };

    const createFileTreeFromStructure = (structure: any, basePath: string = ''): FileNode[] => {
        const tree: FileNode[] = [];

        for (const [name, item] of Object.entries(structure)) {
            const currentPath = basePath ? `${basePath}/${name}` : name;

            // Skip system files and folders
            if (isSystemFile(currentPath)) {
                continue;
            }

            if ((item as any).type === 'folder') {
                const children: FileNode[] = [];

                // Handle children array from ECS structure
                if ((item as any).children && Array.isArray((item as any).children)) {
                    const childrenObj: any = {};
                    for (const child of (item as any).children) {
                        childrenObj[child.name] = child;
                    }
                    children.push(...createFileTreeFromStructure(childrenObj, currentPath));
                }

                const folderNode: FileNode = {
                    name,
                    path: currentPath,
                    type: 'folder',
                    children,
                    isExpanded: false // Default to collapsed - will be overridden by persisted state
                };
                tree.push(folderNode);
            } else {
                const fileNode: FileNode = {
                    name,
                    path: currentPath,
                    type: 'file',
                    content: (item as any).content,
                    language: (item as any).language,
                    lastModified: new Date((item as any).lastModified)
                };
                tree.push(fileNode);
            }
        }

        return tree;
    };

    const flattenStructureToFiles = (structure: any, basePath: string = ''): { [path: string]: string } => {
        const files: { [path: string]: string } = {};

        for (const [name, item] of Object.entries(structure)) {
            const currentPath = basePath ? `${basePath}/${name}` : name;

            // Skip system files
            if (isSystemFile(currentPath)) {
                continue;
            }

            if ((item as any).type === 'folder' && (item as any).children) {
                // Convert children array to object for recursive processing
                const childrenObj: any = {};
                for (const child of (item as any).children) {
                    childrenObj[child.name] = child;
                }
                Object.assign(files, flattenStructureToFiles(childrenObj, currentPath));
            } else {
                files[currentPath] = (item as any).content || '';
            }
        }

        return files;
    };

    const getLanguageFromFilename = (filename: string): string => {
        const ext = filename.split('.').pop()?.toLowerCase();
        const langMap: { [key: string]: string } = {
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'json': 'json',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'yml': 'yaml',
            'yaml': 'yaml',
            'xml': 'xml',
            'md': 'markdown',
            'txt': 'text',
            'env': 'bash',
            'sh': 'bash'
        };
        return langMap[ext || ''] || 'text';
    };

    // Simple file hiding list - add EXACT file paths you want to hide
    // Note: Now only exact matches are hidden, so be specific with paths
    const hiddenFiles = [
        // Container system files (at root level)
        'server.js',           // Hides only root server.js, not backend/server.js
        'health.json',         // Hides only root health.json
        'README.md',           // Hides only root README.md, not backend/README.md

        // Specific paths you want to hide:
        'logs',                // Hides logs directory
        'frontend/node_modules',
        'frontend/package-lock.json',
        'backend/node_modules',
        'backend/package-lock.json',

        // Add more exact paths here as needed:
        // 'frontend/.gitignore',
        // 'backend/.env',
        // 'docker-compose.yml',
    ];

    // Filter out system files and show only LLM-generated files
    const isSystemFile = (filePath: string): boolean => {
        // Only hide files that are EXACTLY in the hidden list
        // This means you need to specify the exact path to hide specific files
        return hiddenFiles.includes(filePath);
    };

    // Filter file tree to show only LLM-generated files
    const filterFileTree = (nodes: FileNode[]): FileNode[] => {
        return nodes.filter(node => {
            // Filter out system files
            if (isSystemFile(node.path)) {
                return false;
            }

            // For folders, recursively filter children
            if (node.type === 'folder' && node.children) {
                const filteredChildren = filterFileTree(node.children);
                // Only include folder if it has non-system children
                if (filteredChildren.length > 0) {
                    node.children = filteredChildren;
                    return true;
                }
                return false;
            }

            // Include non-system files
            return true;
        });
    };

    // Load file content from ECS workspace
    const loadFileContent = async (filePath: string, setAsSelected: boolean = false) => {
        try {
            console.log('📡 Fetching file content from ECS workspace:', filePath);

            const data = await ecsWorkspaceAPI.readFile(workspaceNameRef.current, filePath);
            console.log('📡 API Response for file:', filePath, data);

            if (data.success && data.data) {
                // Validate that we have proper content
                let fileContent = '';

                if (data.data && typeof data.data.content === 'string') {
                    // Standard case: data.data is a FileSystemFile object with content property
                    fileContent = data.data.content;
                } else if (typeof data.data === 'string') {
                    // Fallback: content is directly in data.data as string
                    fileContent = data.data;
                } else if (data.data && data.data.content && typeof data.data.content === 'object') {
                    // Special case: content is an object (like health.json response), stringify it
                    fileContent = JSON.stringify(data.data.content, null, 2);
                } else {
                    console.warn('⚠️ Unexpected file content format for:', filePath, data.data);
                    fileContent = JSON.stringify(data.data, null, 2); // Convert entire data to readable JSON
                }

                console.log('📂 File content loaded:', filePath, 'length:', fileContent.length, 'type:', typeof fileContent);

                // Only update if content is not empty and different from current
                setWorkspace(prev => {
                    const currentContent = prev.files[filePath];
                    if (fileContent.trim() === '' && currentContent && currentContent.trim() !== '') {
                        console.log('📂 Skipping update for empty content when current content exists:', filePath);
                        return prev;
                    }
                    
                    if (currentContent === fileContent) {
                        console.log('📂 Skipping update for identical content:', filePath);
                        return prev;
                    }

                    const updatedWorkspace = {
                        ...prev,
                        files: {
                            ...prev.files,
                            [filePath]: fileContent
                        },
                        savedFiles: {
                            ...prev.savedFiles,
                            [filePath]: fileContent
                        },
                        ...(setAsSelected && { selectedFile: filePath })
                    };

                    console.log('📂 Updated workspace with file content:', filePath, 'length:', fileContent.length);
                    return updatedWorkspace;
                });

                return fileContent;
            } else {
                console.error('❌ Failed to load file content:', data.message);
                setError(`Failed to load file: ${filePath}`);
                return null;
            }
        } catch (error: any) {
            console.error('❌ Error fetching file content:', error);
            setError(`Error loading file: ${filePath}`);
            return null;
        }
    };

    const handleFileSelect = async (filePath: string) => {
        console.log('📂 Selecting file:', filePath);

        try {
            // Set selected file immediately for UI feedback
            setWorkspace(prev => ({
                ...prev,
                selectedFile: filePath
            }));

            // Persist selected file state and expand its ancestors
            const currentUIState = loadWorkspaceUIState();
            const expandedWithAncestors = withAncestorsExpanded(new Set(currentUIState.expandedFolders), filePath);
            saveWorkspaceUIState({
                expandedFolders: expandedWithAncestors,
                selectedFile: filePath
            });

            // Update tree to reveal the selected file
            setWorkspace(prev => ({
                ...prev,
                fileTree: applyUIStateToFileTree(prev.fileTree, expandedWithAncestors)
            }));

            // If file content is already loaded and not empty, no need to fetch
            if (workspace.files[filePath] && workspace.files[filePath].trim() !== '') {
                console.log('📂 File content already loaded:', filePath);
                return;
            }

            // Only fetch if we haven't already loaded this file or if current content is empty
            if (!loadedFilesRef.current.has(filePath) || !workspace.files[filePath] || workspace.files[filePath].trim() === '') {
                // Fetch file content from ECS workspace
                setIsLoading(true);
                loadedFilesRef.current.add(filePath);
                await loadFileContent(filePath);
            }
        } catch (error: any) {
            console.error('❌ Error in handleFileSelect:', error);

            // Ensure we don't leave the UI in a broken state
            setWorkspace(prev => ({
                ...prev,
                selectedFile: null
            }));

            // Persist cleared selected file state
            const currentUIState = loadWorkspaceUIState();
            saveWorkspaceUIState({
                expandedFolders: currentUIState.expandedFolders,
                selectedFile: null
            });
        } finally {
            setIsLoading(false);
        }
        // Trigger lifetime check on browsing
        maybeExtendLifetime(false);
    };

    const handleEditorChange = (value: string | undefined) => {
        if (!workspace.selectedFile || value === undefined) return;

        setWorkspace(prev => {
            const newFiles = { ...prev.files, [prev.selectedFile!]: value };
            const hasChanges = Object.keys(newFiles).some(
                path => newFiles[path] !== prev.savedFiles[path]
            );

            return {
                ...prev,
                files: newFiles,
                hasUnsavedChanges: hasChanges
            };
        });

        // Trigger auto-save if enabled
        if (isAutoSaveEnabled) {
            // Clear existing timeout
            if (autoSaveTimeoutRef.current) {
                clearTimeout(autoSaveTimeoutRef.current);
            }
            
            // Set new timeout for auto-save (1 second debounce)
            autoSaveTimeoutRef.current = window.setTimeout(() => {
                handleAutoSave();
            }, 15000);
        }

        // Trigger lifetime check on typing
        maybeExtendLifetime(false);
    };

    const handleSave = async () => {
        if (!workspace.hasUnsavedChanges) return;

        setIsLoading(true);
        try {
            // Calculate which files have been modified by comparing current files with saved files
            const modifiedFiles: { [filePath: string]: string } = {};
            let modifiedCount = 0;

            for (const [filePath, currentContent] of Object.entries(workspace.files)) {
                const savedContent = workspace.savedFiles[filePath];
                // Include files that are new (not in savedFiles) or have different content
                if (savedContent === undefined || currentContent !== savedContent) {
                    modifiedFiles[filePath] = currentContent;
                    modifiedCount++;
                }
            }

            if (modifiedCount === 0) {
                console.log('📁 No files have been modified, skipping save');
                setWorkspace(prev => ({ ...prev, hasUnsavedChanges: false }));
                return;
            }

            console.log(`💾 Saving ${modifiedCount} modified file(s) to ECS workspace:`, Object.keys(modifiedFiles));

            // Save only the modified files to ECS workspace
            const data = await ecsWorkspaceAPI.saveFiles(workspaceNameRef.current, modifiedFiles);

            if (data.success) {
                setWorkspace(prev => ({
                    ...prev,
                    savedFiles: { ...prev.files },
                    hasUnsavedChanges: false
                }));

                console.log(`✅ Successfully saved ${modifiedCount} modified file(s) to ECS workspace:`, Object.keys(modifiedFiles));
            } else {
                throw new Error(data.message || 'Failed to save files to ECS workspace');
            }
        } catch (error: any) {
            console.error('❌ Error saving files to ECS workspace:', error);
            setError(error.message || 'Failed to save files to ECS workspace');
        } finally {
            setIsLoading(false);
        }
    };

    const handleAutoSave = async () => {
        // Use the latest workspace state from the ref
        const latestWorkspace = latestWorkspaceRef.current;

        if (!latestWorkspace.hasUnsavedChanges || isLoading || isAutoSaving) return;

        setIsAutoSaving(true);
        try {
            console.log('🔄 Auto-saving files...');
            
            // Calculate which files have been modified by comparing current files with saved files
            const modifiedFiles: { [filePath: string]: string } = {};
            let modifiedCount = 0;

            for (const [filePath, currentContent] of Object.entries(latestWorkspace.files)) {
                const savedContent = latestWorkspace.savedFiles[filePath];
                // Include files that are new (not in savedFiles) or have different content
                if (savedContent === undefined || currentContent !== savedContent) {
                    modifiedFiles[filePath] = currentContent;
                    modifiedCount++;
                }
            }

            if (modifiedCount === 0) {
                console.log('📁 Auto-save: No files have been modified, skipping save');
                setWorkspace(prev => ({ ...prev, hasUnsavedChanges: false }));
                return;
            }

            console.log(`💾 Auto-saving ${modifiedCount} modified file(s) to ECS workspace:`, Object.keys(modifiedFiles));

            // Save only the modified files to ECS workspace
            const data = await ecsWorkspaceAPI.saveFiles(workspaceNameRef.current, modifiedFiles);

            if (data.success) {
                setWorkspace(prev => ({
                    ...prev,
                    savedFiles: { ...prev.files },
                    hasUnsavedChanges: false
                }));

                console.log(`✅ Auto-saved ${modifiedCount} modified file(s) to ECS workspace:`, Object.keys(modifiedFiles));
            } else {
                throw new Error(data.message || 'Failed to auto-save files to ECS workspace');
            }
        } catch (error: any) {
            console.error('❌ Error auto-saving files to ECS workspace:', error);
            // Don't show error for auto-save failures as it's automatic
        } finally {
            setIsAutoSaving(false);
        }
    };

    const handleRevert = async () => {
        setIsLoading(true);
        try {
            console.log('🔄 Reverting ECS workspace to last saved state');

            // For ECS workspace, we'll re-fetch the current structure to revert changes
            await fetchWorkspaceStructure();

            console.log('✅ Workspace reverted to ECS workspace state successfully');
        } catch (error: any) {
            console.error('❌ Error reverting ECS workspace:', error);
            setError(error.message || 'Failed to revert ECS workspace');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefresh = async () => {
        console.log('🔄 MonacoWorkspace: Manual refresh triggered');
        setIsInitialized(false);
        setIsBuildComplete(false); // Reset build completion state
        loadedFilesRef.current.clear(); // Reset loaded files tracking
        
        // Clear current workspace state to force fresh load
        setWorkspace({
            files: {},
            savedFiles: {},
            fileTree: [],
            selectedFile: null,
            hasUnsavedChanges: false
        });
        
        await initializeWorkspace();
    };

    // Note: Helper functions for workspace structure are defined above

    // Note: File updates from chat responses are now handled through ECS workspace refresh

    // Expose preview control function globally for build page integration
    useEffect(() => {
        if (interviewUuid) {
            (window as any)[`monacoWorkspace_${interviewUuid}`] = {
                setPreviewRunning: () => setPreviewStatus('running')
            };
        }

        return () => {
            if (interviewUuid) {
                delete (window as any)[`monacoWorkspace_${interviewUuid}`];
            }
        };
    }, [interviewUuid]);

    // Listen for preview events and build completion events from Build page
    useEffect(() => {
        const handlePreviewStarted = (event: CustomEvent) => {
            const { previewUrl: eventPreviewUrl, backendUrl: eventBackendUrl } = event.detail || {};
            setPreviewStatus('running');
            setPreviewError(null);

            if (eventPreviewUrl) {
                setPreviewUrl(eventPreviewUrl);
                // Refresh the iframe with the new URL
                setTimeout(() => {
                    const iframe = document.getElementById('live-preview-iframe') as HTMLIFrameElement;
                    if (iframe) {
                        iframe.src = eventPreviewUrl;
                    }
                }, 1000); // Small delay to ensure server is ready
            }

            // Handle backend URL if provided
            if (eventBackendUrl) {
                setBackendUrl(eventBackendUrl);
                setBackendStatus('running');
            } else {
                setBackendStatus('idle');
                setBackendUrl(null);
            }
        };

        const handleBuildComplete = (event: CustomEvent) => {
            const { interviewUuid: eventInterviewUuid, codeBlocksCount, filesUploaded, uploadSuccess, uploadedFiles } = event.detail || {};
            console.log('🔄 Monaco workspace received build complete event:', {
                eventInterviewUuid,
                codeBlocksCount,
                filesUploaded,
                uploadSuccess,
                uploadedFiles
            });

            if (eventInterviewUuid === interviewUuid) {
                console.log('🔄 Refreshing workspace structure after build completion');
                setIsBuildComplete(true);

                // Retry fetching workspace structure until we have files or max retries
                const retryFetchWithFiles = async (attempt = 0) => {
                    const maxAttempts = 5; // Reduced from 10 to 5 attempts
                    const delay = 2000; // Increased delay to 2 seconds

                    console.log(`📡 MonacoWorkspace: Attempt ${attempt + 1}/${maxAttempts} to fetch workspace with files`);

                    try {
                        const data = await ecsWorkspaceAPI.getStructure(workspaceNameRef.current);

                        // Capture workspaceId for lifecycle API usage
                        const wsId = data?.data?.workspaceInfo?.workspaceId || data?.workspaceInfo?.workspaceId;
                        if (wsId) {
                            setCurrentWorkspaceId(wsId);
                            // Get initial lifetime status immediately
                            maybeExtendLifetime(true);
                        }

                        if (data.success && data.data.structure) {
                            const files = flattenStructureToFiles(data.data.structure);
                            const fileCount = Object.keys(files).length;

                            console.log(`📁 MonacoWorkspace: Found ${fileCount} files in ECS workspace`);

                            if (fileCount > 0) {
                                // We have files! Update workspace and call onWorkspaceReady
                                const fileTree = createFileTreeFromStructure(data.data.structure);
                                const foundFiles = Object.keys(files);

                                console.log('📁 MonacoWorkspace: Files comparison:', {
                                    expectedFiles: uploadedFiles || [],
                                    foundFiles: foundFiles,
                                    expectedCount: (uploadedFiles || []).length,
                                    foundCount: fileCount
                                });

                                // Load persisted UI state and apply it
                                const uiState = loadWorkspaceUIState();
                                const selectedFile = uiState.selectedFile && files[uiState.selectedFile] !== undefined ? uiState.selectedFile : Object.keys(files)[0] || null;
                                const expandedWithAncestors = withAncestorsExpanded(new Set(uiState.expandedFolders), selectedFile);
                                const updatedFileTree = applyUIStateToFileTree(fileTree, expandedWithAncestors);

                                setWorkspace(prev => {
                                    // Preserve any unsaved local edits
                                    const unsavedPaths = Object.keys(prev.files).filter(p => (prev.files[p] ?? '') !== (prev.savedFiles[p] ?? ''));
                                    const mergedFiles: { [path: string]: string } = { ...files };
                                    
                                    // Don't overwrite existing non-empty content with empty strings from structure
                                    for (const p of unsavedPaths) {
                                        if (prev.files[p] && prev.files[p].trim() !== '') {
                                            mergedFiles[p] = prev.files[p];
                                        }
                                    }

                                    // Compute new savedFiles without clobbering unsaved paths
                                    const newSavedFiles: { [path: string]: string } = { ...prev.savedFiles };
                                    for (const p of Object.keys(files)) {
                                        if (!unsavedPaths.includes(p)) {
                                            newSavedFiles[p] = files[p] as any;
                                        }
                                    }

                                    const prevPaths = Object.keys(prev.files);
                                    const nextPaths = Object.keys(mergedFiles);
                                    const samePathSet = prevPaths.length === nextPaths.length && prevPaths.every(p => nextPaths.includes(p));
                                    const sameContents = samePathSet && prevPaths.every(p => (prev.files[p] ?? '') === (mergedFiles[p] ?? ''));
                                    if (sameContents) {
                                        return prev; // No changes; avoid triggering editor refresh
                                    }

                                    return {
                                        ...prev,
                                        files: mergedFiles,
                                        savedFiles: newSavedFiles,
                                        fileTree: updatedFileTree,
                                        selectedFile,
                                        hasUnsavedChanges: unsavedPaths.length > 0 || Object.keys(mergedFiles).some(p => (mergedFiles[p] ?? '') !== (newSavedFiles[p] ?? ''))
                                    };
                                });

                                // Persist updated expanded folders including ancestors
                                saveWorkspaceUIState({
                                    expandedFolders: expandedWithAncestors,
                                    selectedFile
                                });

                                // Auto-load content for the selected file if it's not already loaded and has content
                                if (selectedFile && !loadedFilesRef.current.has(selectedFile) && files[selectedFile] && files[selectedFile].trim() !== '') {
                                    console.log('📂 Auto-loading content for restored selected file after build:', selectedFile);
                                    loadedFilesRef.current.add(selectedFile);
                                    loadFileContent(selectedFile);
                                } else if (selectedFile && (!files[selectedFile] || files[selectedFile].trim() === '')) {
                                    // If selected file exists but has no content, try to load it from the server
                                    console.log('📂 Selected file has no content after build, loading from server:', selectedFile);
                                    loadedFilesRef.current.add(selectedFile);
                                    loadFileContent(selectedFile);
                                }

                                console.log('✅ MonacoWorkspace: Build complete with files loaded');
                                console.log('🎯 MonacoWorkspace: Files loaded, workspace is ready for use');
                                // Note: onWorkspaceReady is now managed by Build page through isWorkspaceReady state
                                return;
                            }
                        }

                        // No files yet, retry if we haven't exceeded max attempts
                        if (attempt < maxAttempts - 1) {
                            console.log(`⏳ MonacoWorkspace: No files yet, retrying in ${delay}ms...`);
                            fetchTimeoutRef.current = window.setTimeout(() => retryFetchWithFiles(attempt + 1), delay);
                        } else {
                            console.log('⚠️ MonacoWorkspace: Max attempts reached');
                            console.log('⚠️ MonacoWorkspace: Expected files:', uploadedFiles || []);
                            console.log('🎯 MonacoWorkspace: Max retries reached, workspace may be empty');
                            // Note: onWorkspaceReady is now managed by Build page through isWorkspaceReady state
                        }
                    } catch (error) {
                        console.error(`❌ MonacoWorkspace: Error fetching workspace (attempt ${attempt + 1}):`, error);

                        if (attempt < maxAttempts - 1) {
                            fetchTimeoutRef.current = window.setTimeout(() => retryFetchWithFiles(attempt + 1), delay);
                        } else {
                            console.log('⚠️ MonacoWorkspace: Max attempts reached after errors');
                            console.log('⚠️ MonacoWorkspace: Expected files:', uploadedFiles || []);
                            console.log('🎯 MonacoWorkspace: Max retries with errors, workspace may be empty');
                            // Note: onWorkspaceReady is now managed by Build page through isWorkspaceReady state
                        }
                    }
                };

                // Start the retry process immediately
                retryFetchWithFiles();
            }
        };

        const handleChatUpdate = (event: CustomEvent) => {
            const { interviewUuid: eventInterviewUuid, filesCount } = event.detail || {};
            console.log('🔄 Monaco workspace received chat update event:', { eventInterviewUuid, filesCount });

            if (eventInterviewUuid === interviewUuid) {
                console.log('🔄 Refreshing workspace structure after chat file update');
                // Wait a moment for ECS workspace to be updated, then refresh
                setTimeout(() => {
                    fetchWorkspaceStructure();
                }, 1000);
            }
        };

        window.addEventListener('preview-started', handlePreviewStarted as EventListener);
        window.addEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
        window.addEventListener('ecs-workspace-chat-update', handleChatUpdate as EventListener);

        return () => {
            window.removeEventListener('preview-started', handlePreviewStarted as EventListener);
            window.removeEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
            window.removeEventListener('ecs-workspace-chat-update', handleChatUpdate as EventListener);
        };
    }, [interviewUuid]);

    const toggleFolder = (folderPath: string) => {
        const updateTree = (nodes: FileNode[]): FileNode[] => {
            return nodes.map(node => {
                if (node.path === folderPath && node.type === 'folder') {
                    return { ...node, isExpanded: !node.isExpanded };
                }
                if (node.children) {
                    return { ...node, children: updateTree(node.children) };
                }
                return node;
            });
        };

        // Update the file tree state
        setWorkspace(prev => {
            const updatedTree = updateTree(prev.fileTree);
            
            // Persist folder expansion state
            const currentUIState = loadWorkspaceUIState();
            const expandedFolders = new Set(currentUIState.expandedFolders);
            
            // Find the folder and check its new expanded state
            const findFolderInTree = (nodes: FileNode[]): boolean | null => {
                for (const node of nodes) {
                    if (node.path === folderPath && node.type === 'folder') {
                        return node.isExpanded ?? false;
                    }
                    if (node.children) {
                        const result = findFolderInTree(node.children);
                        if (result !== null) return result;
                    }
                }
                return null;
            };
            
            const isExpanded = findFolderInTree(updatedTree);
            if (isExpanded !== null) {
                if (isExpanded) {
                    expandedFolders.add(folderPath);
                } else {
                    expandedFolders.delete(folderPath);
                }
                
                saveWorkspaceUIState({
                    ...currentUIState,
                    expandedFolders
                });
            }
            
            return {
                ...prev,
                fileTree: updatedTree
            };
        });
    };

    // Preview control functions
    const handleStartPreview = async () => {
        if (!interviewUuid) return;

        setPreviewStatus('starting');
        setPreviewError(null);

        try {
            const workspaceName = `workspace_${interviewUuid}`;
            const data = await ecsWorkspaceAPI.startPreview(workspaceName);

            if (data.success) {
                setPreviewStatus('running');
                setPreviewUrl(data.previewUrl); // Store the dynamic preview URL

                // Handle backend URL if available
                if (data.backendUrl) {
                    setBackendUrl(data.backendUrl);
                    setBackendStatus('running');
                } else {
                    setBackendStatus('idle');
                }

                // Refresh the iframe to show the preview
                const iframe = document.getElementById('live-preview-iframe') as HTMLIFrameElement;
                if (iframe && data.previewUrl) {
                    iframe.src = data.previewUrl; // Use the actual ECS preview URL
                }
            } else {
                setPreviewStatus('error');
                setPreviewError(data.message || 'Failed to start ECS preview server');
            }
        } catch (error: any) {
            console.error('Error starting ECS preview:', error);
            setPreviewStatus('error');
            setPreviewError(error.message || 'Failed to start ECS preview server');
        }
    };

    const handleStopPreview = async () => {
        if (!interviewUuid) return;

        try {
            const workspaceName = `workspace_${interviewUuid}`;
            const data = await ecsWorkspaceAPI.stopPreview(workspaceName);

            if (data.success) {
                setPreviewStatus('idle');
                setPreviewError(null);
                setPreviewUrl(null); // Clear the preview URL
                setBackendStatus('idle');
                setBackendUrl(null); // Clear the backend URL
            } else {
                setPreviewError(data.message || 'Failed to stop ECS preview server');
            }
        } catch (error: any) {
            console.error('Error stopping ECS preview:', error);
            setPreviewError(error.message || 'Failed to stop ECS preview server');
        }
    };

    const handleOpenInTab = () => {
        if (previewUrl) {
            window.open(previewUrl, '_blank');
        }
    };

    const handleRefreshPreview = () => {
        const iframe = document.getElementById('live-preview-iframe') as HTMLIFrameElement;
        if (iframe) {
            iframe.src = iframe.src; // Force refresh
        }
    };





    const togglePreviewVisibility = () => {
        setIsPreviewOpen(prev => {
            const next = !prev;
            const currentUIState = loadWorkspaceUIState();
            saveWorkspaceUIState({
                expandedFolders: currentUIState.expandedFolders,
                selectedFile: currentUIState.selectedFile,
                isPreviewOpen: next,
                isExplorerOpen: currentUIState.isExplorerOpen
            });
            return next;
        });
    };

    const toggleExplorerVisibility = () => {
        setIsExplorerOpen(prev => {
            const next = !prev;
            const currentUIState = loadWorkspaceUIState();
            saveWorkspaceUIState({
                expandedFolders: currentUIState.expandedFolders,
                selectedFile: currentUIState.selectedFile,
                isPreviewOpen: currentUIState.isPreviewOpen,
                isExplorerOpen: next
            });
            return next;
        });
    };

    const renderFileTree = (nodes: FileNode[], depth: number = 0): React.ReactNode => {
        return nodes.map(node => (
            <React.Fragment key={node.path}>
                <ListItemButton
                    onClick={() => {
                        if (node.type === 'folder') {
                            toggleFolder(node.path);
                        } else {
                            handleFileSelect(node.path);
                        }
                    }}
                    selected={workspace.selectedFile === node.path}
                    sx={{
                        pl: 2 + depth * 2,
                        py: 0.5,
                        minHeight: 32,
                        cursor: 'pointer',
                        '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.1)'
                        },
                        '&.Mui-selected': {
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            '&:hover': {
                                backgroundColor: 'rgba(102, 126, 234, 0.2)'
                            }
                        }
                    }}
                >
                    <ListItemIcon sx={{ minWidth: 24, color: '#ffffff' }}>
                        {node.type === 'folder' ? (
                            node.isExpanded ? <FolderOpenIcon fontSize="small" sx={{ color: '#ffd700' }} /> : <FolderIcon fontSize="small" sx={{ color: '#ffd700' }} />
                        ) : (
                            <FileIcon fontSize="small" sx={{ color: '#87ceeb' }} />
                        )}
                    </ListItemIcon>
                    <ListItemText
                        primary={node.name}
                        primaryTypographyProps={{
                            fontSize: '0.875rem',
                            fontWeight: workspace.selectedFile === node.path ? 'bold' : 'normal',
                            color: '#ffffff' // White text for better visibility
                        }}
                    />
                    {node.type === 'folder' && (
                        <IconButton size="small" onClick={(e) => {
                            e.stopPropagation();
                            toggleFolder(node.path);
                        }} sx={{ color: '#ffffff' }}>
                            {node.isExpanded ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
                        </IconButton>
                    )}
                </ListItemButton>
                {node.type === 'folder' && node.isExpanded && node.children && (
                    <Collapse in={node.isExpanded}>
                        {renderFileTree(node.children, depth + 1)}
                    </Collapse>
                )}
            </React.Fragment>
        ));
    };

    const selectedFileContent = (() => {
        if (!workspace.selectedFile) return '';

        const content = workspace.files[workspace.selectedFile];
        if (content === undefined || content === null) return '';

        // Ensure content is always a string
        if (typeof content === 'string') return content;

        // If content is not a string, convert it safely
        try {
            return JSON.stringify(content, null, 2);
        } catch (error) {
            console.error('❌ Error converting file content to string:', error);
            return String(content);
        }
    })();

    // Debug log for selected file content
    useEffect(() => {
        if (workspace.selectedFile) {
            console.log('📂 Selected file content debug:', {
                file: workspace.selectedFile,
                hasContent: !!workspace.files[workspace.selectedFile],
                contentLength: workspace.files[workspace.selectedFile]?.length || 0,
                contentPreview: workspace.files[workspace.selectedFile]?.substring(0, 100) || 'empty'
            });
        }
    }, [workspace.selectedFile, workspace.files]);

    const selectedFileLanguage = workspace.selectedFile ? getLanguageFromFilename(workspace.selectedFile) : 'text';

    return (
        <Paper
            elevation={3}
            data-workspace-root
            sx={{
                height: height || 'calc(100vh - 200px)', // Use provided height or viewport-based
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                flex: 1, // Always allow growing
                minHeight: minHeight || '600px', // Ensure minimum usable height
                borderRadius: 2,
                backgroundColor: '#1e1e1e'
            }}>
            {/* Temporary test button - remove after testing */}
            {process.env.NODE_ENV === 'developmen' && (
                <Box sx={{ p: 1, bgcolor: '#fff3cd', borderBottom: '1px solid #ffeaa7' }}>
                    <Button 
                        size="small" 
                        variant="outlined" 
                        onClick={() => {
                            console.log('🧪 Test button clicked - opening modal');
                            setExtendModalOpen(true);
                        }}
                        sx={{ fontSize: '0.75rem' }}
                    >
                        🧪 Test Extension Modal
                    </Button>
                </Box>
            )}
            {/* Workspace Header */}
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 1,
                    backgroundColor: '#2d2d2d',
                    borderBottom: '1px solid #444',
                    flexShrink: 0
                }}
            >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                        Workspace
                    </Typography>
                    {isLoading && <CircularProgress size={16} sx={{ color: 'orange' }} />}
                    {typeof minutesRemaining === 'number' && !isExpired && (
                        <Tooltip title={`Time remaining before auto-cleanup: ${minutesRemaining} minute(s)`}>
                            <Typography
                                variant="caption"
                                sx={{
                                    ml: 1,
                                    px: 0.75,
                                    py: 0.25,
                                    borderRadius: 1,
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    height: 18,
                                    lineHeight: '18px',
                                    backgroundColor: minutesRemaining <= 15 ? 'rgba(255, 152, 0, 0.2)' : 'rgba(255, 255, 255, 0.08)',
                                    color: minutesRemaining <= 15 ? '#ffb74d' : '#cccccc'
                                }}
                            >
                                {minutesRemaining} min left
                            </Typography>
                        </Tooltip>
                    )}
                    {isExpired && (
                        <Tooltip title="Workspace has expired and will be cleaned up soon">
                            <Typography
                                variant="caption"
                                sx={{
                                    ml: 1,
                                    px: 0.75,
                                    py: 0.25,
                                    borderRadius: 1,
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    height: 18,
                                    lineHeight: '18px',
                                    backgroundColor: 'rgba(244, 67, 54, 0.2)',
                                    color: '#f44336'
                                }}
                            >
                                Expired - will be cleaned up soon
                            </Typography>
                        </Tooltip>
                    )}
                    {typeof minutesRemaining !== 'number' && !isExpired && (
                        <Typography
                            variant="caption"
                            sx={{
                                ml: 1,
                                color: '#9e9e9e',
                                opacity: 0.8,
                                display: 'inline-flex',
                                alignItems: 'center',
                                height: 18,
                                lineHeight: '18px'
                            }}
                        >
                            Calculating workspace remaining lifetime...
                        </Typography>
                    )}
                </Box>

                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                        size="small"
                        onClick={toggleExplorerVisibility}
                        startIcon={isExplorerOpen ? <FolderOpenIcon /> : <FolderIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        {isExplorerOpen ? 'Hide Explorer' : 'Show Explorer'}
                    </Button>
                    <Tooltip
                        title={
                            isAutoSaveEnabled 
                                ? "Auto-save is enabled - files save automatically after 15 second of inactivity" 
                                : "Auto-save is disabled - click Save button to save changes manually"
                        }
                    >
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={isAutoSaveEnabled}
                                    onChange={(e) => setIsAutoSaveEnabled(e.target.checked)}
                                    size="small"
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#4caf50',
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#4caf50',
                                        },
                                    }}
                                />
                            }
                            label={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                    <Typography variant="caption" sx={{ color: '#cccccc', fontSize: '0.75rem' }}>
                                        Auto-Save
                                    </Typography>
                                    {isAutoSaving && (
                                        <CircularProgress size={12} sx={{ color: '#4caf50', ml: 0.5 }} />
                                    )}
                                </Box>
                            }
                            sx={{
                                margin: 0,
                                '& .MuiFormControlLabel-label': {
                                    fontSize: '0.75rem'
                                }
                            }}
                        />
                    </Tooltip>

                    <Tooltip
                        title={
                            !workspace.hasUnsavedChanges
                                ? "No unsaved changes to save"
                                : isAutoSaveEnabled 
                                    ? "Manual save (auto-save is enabled)"
                                    : "Save changes to ECS workspace"
                        }
                    >
                        <span>
                            <Button
                                size="small"
                                onClick={handleSave}
                                disabled={!workspace.hasUnsavedChanges || isLoading}
                                startIcon={<SaveIcon />}
                                sx={{
                                    minWidth: 'auto',
                                    color: workspace.hasUnsavedChanges ? '#4caf50' : '#cccccc',
                                    opacity: isAutoSaveEnabled ? 0.7 : 1, // Less prominent when auto-save is enabled
                                    '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                                    '&:disabled': {
                                        color: '#888888 !important',
                                        '& .MuiSvgIcon-root': {
                                            color: '#888888 !important'
                                        }
                                    }
                                }}
                            >
                                Save
                            </Button>
                        </span>
                    </Tooltip>

                    <Tooltip
                        title={
                            !workspace.hasUnsavedChanges
                                ? "No unsaved changes to revert"
                                : "Revert changes to last saved state"
                        }
                    >
                        <span>
                            <Button
                                size="small"
                                onClick={handleRevert}
                                disabled={!workspace.hasUnsavedChanges || isLoading}
                                startIcon={<RevertIcon />}
                                sx={{
                                    minWidth: 'auto',
                                    color: workspace.hasUnsavedChanges ? '#ff9800' : '#cccccc',
                                    '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                                    '&:disabled': {
                                        color: '#888888 !important',
                                        '& .MuiSvgIcon-root': {
                                            color: '#888888 !important'
                                        }
                                    }
                                }}
                            >
                                Revert
                            </Button>
                        </span>
                    </Tooltip>

                    <Button
                        size="small"
                        onClick={handleRefresh}
                        startIcon={<RefreshIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        Refresh
                    </Button>

                    <Button
                        size="small"
                        onClick={togglePreviewVisibility}
                        startIcon={isPreviewOpen ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        sx={{
                            minWidth: 'auto',
                            color: '#cccccc',
                            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
                        }}
                    >
                        {isPreviewOpen ? 'Hide Preview' : 'Show Preview'}
                    </Button>
                </Box>
            </Box>

            {/* Error Display */}
            {error && (
                <Alert severity="error" sx={{ m: 1, flexShrink: 0 }}>
                    {error}
                </Alert>
            )}

            {/* Loading State */}
            {isLoading && !isInitialized && (
                <Box
                    sx={{
                        flex: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#1e1e1e',
                        height: '100%'
                    }}
                >
                    <Box sx={{ textAlign: 'center', color: '#cccccc' }}>
                        <CircularProgress size={40} sx={{ mb: 2, color: '#667eea' }} />
                        <Typography variant="body1">
                            Initializing Monaco workspace...
                        </Typography>
                    </Box>
                </Box>
            )}

            {/* Main Workspace Content */}
            {isInitialized && (
                <Box sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: { xs: 'column', lg: 'row' },
                    backgroundColor: '#1e1e1e',
                    overflow: 'hidden',
                    minHeight: 0
                }}>
                    {/* File Explorer */}
                    {isExplorerOpen && (
                        <Box
                            sx={{
                                width: { xs: '100%', lg: 320 },
                                minWidth: { lg: 280 },
                                backgroundColor: '#252526',
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                                flexShrink: 0,
                                borderRight: { lg: '1px solid #444' },
                                // Critical: on small screens, do NOT force full height to avoid hiding editor/preview
                                height: { xs: 'auto', lg: '100%' },
                                maxHeight: { xs: '35vh', lg: 'none' },
                                borderBottom: { xs: '1px solid #444', lg: 'none' }
                            }}
                        >
                            <Box
                                sx={{
                                    p: 1,
                                    borderBottom: '1px solid #444',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between'
                                }}
                            >
                                <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                                    Explorer
                                </Typography>
                            </Box>

                            <List sx={{
                                flex: 1,
                                py: 0,
                                overflow: 'auto',
                                minHeight: 0,
                                maxHeight: '100%',
                                '&::-webkit-scrollbar': { width: '8px' },
                                '&::-webkit-scrollbar-track': { background: '#2d2d2d' },
                                '&::-webkit-scrollbar-thumb': { background: '#555', borderRadius: '4px' },
                                '&::-webkit-scrollbar-thumb:hover': { background: '#777' }
                            }}>
                                {isLoading ? (
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        p: 3,
                                        color: '#cccccc'
                                    }}>
                                        <CircularProgress size={24} sx={{ color: '#667eea', mb: 2 }} />
                                        <Typography variant="body2" sx={{ textAlign: 'center' }}>
                                            {isBuildComplete ? 'Loading workspace files...' : 'Waiting for build completion...'}
                                        </Typography>
                                        <Typography variant="caption" sx={{ textAlign: 'center', mt: 1, opacity: 0.7 }}>
                                            {isBuildComplete ? 'Fetching your generated code' : 'Code generation in progress'}
                                        </Typography>
                                    </Box>
                                ) : workspace.fileTree.length === 0 ? (
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        p: 3,
                                        color: '#888'
                                    }}>
                                        <Typography variant="body2" sx={{ textAlign: 'center' }}>
                                            No files yet
                                        </Typography>
                                        <Typography variant="caption" sx={{ textAlign: 'center', mt: 1 }}>
                                            Files will appear here after code generation
                                        </Typography>
                                    </Box>
                                ) : (
                                    renderFileTree(filterFileTree(workspace.fileTree))
                                )}
                            </List>
                        </Box>
                    )}

                    {/* Editor and Preview */}
                    <Box sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: { xs: 'column', lg: 'row' },
                        height: '100%',
                        overflow: 'hidden',
                        minHeight: 0,
                        minWidth: 0,
                        borderTop: { xs: isExplorerOpen ? '1px solid #444' : 'none', lg: 'none' }
                    }}>
                        {/* Code Editor */}
                        <Box
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                overflow: 'hidden',
                                backgroundColor: '#1e1e1e',
                                borderRight: isPreviewOpen ? { lg: '1px solid #444' } : 'none'
                            }}
                        >
                            {workspace.selectedFile ? (
                                <>
                                    <Box
                                        sx={{
                                            p: 1.5,
                                            borderBottom: '1px solid #444',
                                            backgroundColor: '#2d2d2d',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between'
                                        }}
                                    >
                                        <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                                            {workspace.selectedFile}
                                        </Typography>
                                        {workspace.hasUnsavedChanges && (
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Typography variant="caption" sx={{ color: '#ff9800', fontWeight: 'bold' }}>
                                                    ● Unsaved changes
                                                </Typography>
                                                {isAutoSaveEnabled && (
                                                    <Typography variant="caption" sx={{ color: '#4caf50', fontSize: '0.7rem' }}>
                                                        (auto-save: 15s)
                                                    </Typography>
                                                )}
                                                {isAutoSaving && (
                                                    <CircularProgress size={10} sx={{ color: '#4caf50' }} />
                                                )}
                                            </Box>
                                        )}
                                    </Box>

                                    <Box sx={{
                                        flex: 1,
                                        overflow: 'hidden',
                                        minHeight: 0, // Important for flex shrinking
                                        height: '100%',
                                        position: 'relative' // Ensure Monaco editor can position properly
                                    }}>
                                        <Editor
                                            height="100%"
                                            language={selectedFileLanguage}
                                            value={selectedFileContent}
                                            onChange={handleEditorChange}
                                            onMount={(editor) => {
                                                editorRef.current = editor;
                                            }}
                                            theme="vs-dark"
                                            options={{
                                                minimap: { enabled: window.innerWidth > 1024 }, // Enable minimap only on larger screens
                                                fontSize: window.innerWidth < 768 ? 12 : 14, // Smaller font on mobile
                                                lineNumbers: 'on',
                                                roundedSelection: false,
                                                scrollBeyondLastLine: false,
                                                automaticLayout: true,
                                                wordWrap: 'on',
                                                scrollbar: {
                                                    vertical: 'visible',
                                                    horizontal: 'visible',
                                                    verticalScrollbarSize: window.innerWidth < 768 ? 8 : 12,
                                                    horizontalScrollbarSize: window.innerWidth < 768 ? 8 : 12
                                                },
                                                // Better mobile experience
                                                glyphMargin: window.innerWidth > 768,
                                                folding: window.innerWidth > 768,
                                                lineDecorationsWidth: window.innerWidth < 768 ? 10 : 20,
                                                lineNumbersMinChars: window.innerWidth < 768 ? 3 : 5
                                            }}
                                        />
                                    </Box>
                                </>
                            ) : (
                                <Box
                                    sx={{
                                        flex: 1,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#cccccc',
                                        height: '100%'
                                    }}
                                >
                                    <Typography variant="h6" sx={{ opacity: 0.7 }}>
                                        Select a file to start editing
                                    </Typography>
                                </Box>
                            )}
                        </Box>

                        {/* Live Preview */}
                        {isPreviewOpen && (
                            <Box
                                sx={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    overflow: 'hidden',
                                    backgroundColor: '#ffffff',
                                    minHeight: 0,
                                    height: '100%',
                                    minWidth: 0
                                }}
                            >
                                <Box
                                    sx={{
                                        p: 1.5,
                                        borderBottom: '1px solid #e0e0e0',
                                        backgroundColor: '#f5f5f5',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between'
                                    }}
                                >
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#333' }}>
                                            Live Preview
                                        </Typography>
                                        {backendStatus === 'running' && (
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <Box
                                                    sx={{
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: '50%',
                                                        backgroundColor: '#4caf50'
                                                    }}
                                                />
                                                <Typography variant="caption" sx={{ color: '#666', fontSize: '0.75rem' }}>
                                                    Backend Running
                                                </Typography>
                                            </Box>
                                        )}
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                                        {previewStatus === 'running' && (
                                            <>
                                                <IconButton
                                                    size="small"
                                                    onClick={handleOpenInTab}
                                                    sx={{ color: '#666' }}
                                                    title="Open in New Tab"
                                                >
                                                    <OpenInNewIcon fontSize="small" />
                                                </IconButton>
                                                <IconButton
                                                    size="small"
                                                    onClick={handleStopPreview}
                                                    sx={{ color: '#666' }}
                                                    title="Stop Preview"
                                                >
                                                    <StopIcon fontSize="small" />
                                                </IconButton>
                                            </>
                                        )}
                                        <IconButton
                                            size="small"
                                            onClick={handleRefreshPreview}
                                            sx={{ color: '#666' }}
                                            title="Refresh Preview"
                                        >
                                            <RefreshIcon fontSize="small" />
                                        </IconButton>
                                    </Box>
                                </Box>

                                <Box sx={{
                                    flex: 1,
                                    position: 'relative',
                                    overflow: 'hidden',
                                    minHeight: 0 // Important for flex shrinking
                                }}>
                                    {previewStatus === 'idle' && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                height: '100%',
                                                bgcolor: '#f9f9f9',
                                                color: '#666',
                                                textAlign: 'center',
                                                p: 3
                                            }}
                                        >
                                            <Typography variant="h6" sx={{ mb: 2 }}>
                                                No Preview Available
                                            </Typography>
                                            <Typography variant="body2" sx={{ mb: 3 }}>
                                                Click the Preview button in the top toolbar to start your development servers (frontend + backend if available)
                                            </Typography>
                                            <Button
                                                variant="contained"
                                                onClick={handleStartPreview}
                                                sx={{
                                                    bgcolor: '#667eea',
                                                    '&:hover': { bgcolor: '#5a6fd8' }
                                                }}
                                            >
                                                Start Preview
                                            </Button>
                                        </Box>
                                    )}

                                    {previewStatus === 'starting' && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                height: '100%',
                                                bgcolor: '#f9f9f9',
                                                color: '#666',
                                                textAlign: 'center',
                                                p: 3
                                            }}
                                        >
                                            <CircularProgress size={40} sx={{ mb: 2, color: '#667eea' }} />
                                            <Typography variant="h6" sx={{ mb: 1 }}>
                                                Starting Development Servers...
                                            </Typography>
                                            <Typography variant="body2">
                                                Please wait while we install dependencies and start your frontend and backend services
                                            </Typography>
                                        </Box>
                                    )}

                                    {previewStatus === 'error' && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                height: '100%',
                                                bgcolor: '#fff5f5',
                                                color: '#d32f2f',
                                                textAlign: 'center',
                                                p: 3
                                            }}
                                        >
                                            <Typography variant="h6" sx={{ mb: 2 }}>
                                                Preview Error
                                            </Typography>
                                            <Typography variant="body2" sx={{ mb: 3 }}>
                                                {previewError || 'Failed to start preview server'}
                                            </Typography>
                                            <Button
                                                variant="outlined"
                                                onClick={handleStartPreview}
                                                sx={{
                                                    borderColor: '#667eea',
                                                    color: '#667eea',
                                                    '&:hover': { borderColor: '#5a6fd8', bgcolor: 'rgba(102, 126, 234, 0.1)' }
                                                }}
                                            >
                                                Try Again
                                            </Button>
                                        </Box>
                                    )}

                                    {previewStatus === 'running' && previewUrl && (
                                        <iframe
                                            id="live-preview-iframe"
                                            src={previewUrl}
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                                border: 'none',
                                                backgroundColor: 'white',
                                                display: 'block'
                                            }}
                                            title="Live Preview"
                                            onError={(e) => {
                                                console.error('Preview iframe error:', e);
                                                setPreviewStatus('error');
                                                setPreviewError('ECS preview server is not responding');
                                            }}
                                        />
                                    )}
                                </Box>
                            </Box>
                        )}
                    </Box>
                </Box>
            )}

            {/* Modal alert when ECS workspace is extended */}
            <Dialog
                open={extendModalOpen}
                onClose={() => {
                    console.log(`🔔 Modal closed by user`);
                    setExtendModalOpen(false);
                }}
                aria-labelledby="extension-alert-dialog-title"
                aria-describedby="extension-alert-dialog-description"
                maxWidth="sm"
                fullWidth
                sx={{ zIndex: 9999 }}
                BackdropProps={{
                    style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' }
                }}
            >
                <DialogTitle id="extension-alert-dialog-title" sx={{ 
                    bgcolor: '#4caf50', 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                }}>
                    <RefreshIcon />
                    ECS Workspace Extended
                </DialogTitle>
                <DialogContent sx={{ pt: 3 }}>
                    <Typography variant="body1" id="extension-alert-dialog-description">
                        Your ECS workspace lifetime has been automatically extended by{' '}
                        <strong>{config?.extendLifetimeHours || 1} hour{config?.extendLifetimeHours !== 1 ? 's' : ''}</strong>.
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
                        This ensures your development environment remains active while you continue working.
                    </Typography>
                </DialogContent>
                <DialogActions sx={{ p: 2 }}>
                    <Button 
                        onClick={() => {
                            console.log(`🔔 OK button clicked`);
                            setExtendModalOpen(false);
                        }}
                        variant="contained"
                        sx={{ 
                            bgcolor: '#4caf50',
                            '&:hover': { bgcolor: '#45a049' }
                        }}
                    >
                        OK
                    </Button>
                </DialogActions>
            </Dialog>


        </Paper>
    );
};

export default MonacoWorkspace;